import type { Metadata } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON><PERSON><PERSON>, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  display: 'swap',
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  display: 'swap',
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "WLC Academy",
  description: "Afterschool Program",
  keywords: ["WLC Academy", "Afterschool Program", "Academic Support", "Enrichment Activities", "Safe Learning Environment"],
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
  },
  icons: {
    icon: [
      { url: '/favicon.ico', type: 'image/x-icon' },
      { url: '/images/logo.png', type: 'image/png' }
    ],
    shortcut: [{ url: '/favicon.ico', type: 'image/x-icon' }],
    apple: [{ url: '/images/wlc-logo.png', type: 'image/png' }],
    other: [
      { rel: 'apple-touch-icon-precomposed', url: '/images/wlc-logo.png' }
    ]
  },
  openGraph: {
    type: "website",
    siteName: "WLC Academy",
    title: undefined,
    description: "Afterschool Program",
    images: [
      {
        url: "/images/wlc-logo.png",
        width: 800,
        height: 800,
        alt: "WLC Academy Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: undefined,
    description: "Afterschool Program",
    images: ["/images/wlc-logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/images/wlc-logo.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={`${inter.variable} ${poppins.variable} ${playfair.variable} ${geistMono.variable} antialiased font-sans`}
      >
        {children}
      </body>
    </html>
  );
}
