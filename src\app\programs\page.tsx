"use client";

import React from "react";
import { motion } from "framer-motion";
import { Navigation } from "@/components/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BookOpen, ArrowRight, Lightbulb, Users } from "lucide-react";
import { Footer } from "@/components/footer";
import Link from "next/link";

const programs = [
  {
    id: "afterschool",
    title: "Afterschool Program",
    description: "Comprehensive afterschool program providing academic support and a safe learning environment for students",
    icon: BookOpen,
    features: [
      "Homework assistance and tutoring",
      "Academic support and guidance",
      "Safe and supervised environment",
      "Character building activities",
      "Social skills development",
      "Healthy snacks provided",
      "Educational games and activities",
      "Progress monitoring and reporting"
    ],
    ageRange: "Ages 5-14",
    duration: "Year-round program",
    schedule: "Monday-Friday, 3:00 PM - 6:00 PM",
    location: "WLC Academy Campus",
    bgColor: "bg-[#F3CC5C]",
    iconColor: "text-[#07243C]"
  },
  {
    id: "weekend",
    title: "Weekend Program",
    description: "Enriching weekend activities designed to enhance student learning through creative and engaging experiences",
    icon: Lightbulb,
    features: [
      "Creative learning workshops",
      "Skill development activities",
      "Interactive educational projects",
      "Arts and crafts sessions",
      "Science experiments and exploration",
      "Team building exercises",
      "Cultural and recreational activities",
      "Social engagement opportunities"
    ],
    ageRange: "Ages 5-14",
    duration: "Weekend sessions",
    schedule: "Saturdays & Sundays, 9:00 AM - 3:00 PM",
    location: "WLC Academy Campus",
    bgColor: "bg-[#07243C]",
    iconColor: "text-[#F3CC5C]"
  },
  {
    id: "tutoring",
    title: "Tutoring Program",
    description: "Personalized tutoring services tailored to meet the specific academic needs of individual students",
    icon: Users,
    features: [
      "One-on-one tutoring sessions",
      "Subject-specific academic support",
      "Customized learning plans",
      "Progress tracking and assessment",
      "Flexible scheduling options",
      "Experienced qualified tutors",
      "Test preparation assistance",
      "Study skills development"
    ],
    ageRange: "Ages 5-14",
    duration: "Ongoing support",
    schedule: "Flexible scheduling available",
    location: "WLC Academy Campus or Online",
    bgColor: "bg-[#F3CC5C]",
    iconColor: "text-[#07243C]"
  }
];

export default function ProgramsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <div className="inline-block px-4 py-2 bg-[#F3CC5C] rounded-full text-sm font-medium text-[#07243C] mb-6">
              Our Programs
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-light text-gray-900 mb-6 leading-tight">
              Our Programs
            </h1>
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Comprehensive educational programs designed to support every student's learning journey and individual needs
            </p>
          </motion.div>
        </div>
      </section>

      {/* Program Details */}
      <section className="pb-24 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 items-stretch">
            {programs.map((program, index) => {
              const IconComponent = program.icon;
              return (
                <motion.div
                  key={program.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  id={program.id}
                >
                  <Card className="h-full border border-gray-100 hover:border-[#F3CC5C] transition-all duration-200 hover:shadow-lg flex flex-col">
                    <CardContent className="p-6 flex flex-col h-full">
                      <div className="flex flex-col items-center text-center mb-6">
                        <div className={`w-16 h-16 ${program.bgColor} rounded-xl flex items-center justify-center mb-4`}>
                          <IconComponent className={`w-8 h-8 ${program.iconColor}`} />
                        </div>
                        <h3 className="text-2xl font-medium text-[#07243C] mb-3">{program.title}</h3>
                        <p className="text-gray-600 leading-relaxed text-base">{program.description}</p>
                      </div>

                      <div className="grid grid-cols-1 gap-3 mb-6 text-sm">
                        <div className="flex justify-between items-start">
                          <span className="text-gray-500 min-w-[80px]">Age Range:</span>
                          <span className="text-xs font-medium text-gray-900 text-right">{program.ageRange}</span>
                        </div>
                        <div className="flex justify-between items-start">
                          <span className="text-gray-500 min-w-[80px]">Duration:</span>
                          <span className="text-xs font-medium text-gray-900 text-right">{program.duration}</span>
                        </div>
                        <div className="flex justify-between items-start">
                          <span className="text-gray-500 min-w-[80px]">Schedule:</span>
                          <span className="text-xs font-medium text-gray-900 text-right leading-tight">{program.schedule}</span>
                        </div>
                        <div className="flex justify-between items-start">
                          <span className="text-gray-500 min-w-[80px]">Location:</span>
                          <span className="text-xs font-medium text-gray-900 text-right leading-tight">{program.location}</span>
                        </div>
                      </div>

                      <div className="flex-grow mb-6">
                        <h4 className="font-medium text-gray-900 mb-3 text-base">Program Features:</h4>
                        <div className="grid grid-cols-1 gap-2">
                          {program.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-start text-sm text-gray-600">
                              <div className="w-2 h-2 bg-[#F3CC5C] rounded-full mr-3 flex-shrink-0 mt-1.5"></div>
                              <span className="leading-relaxed">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex flex-col gap-3 mt-auto">
                        <Button className="w-full bg-[#07243C] text-white hover:bg-[#0a2d47] group">
                          <Link href="/contact">
                            Enroll Now
                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                          </Link>
                        </Button>
                        <Button variant="outline" className="w-full text-[#07243C] border-[#07243C] hover:bg-[#07243C] hover:text-white">
                          <Link href="/contact">Contact Us</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Admission Process */}
      {/* <section className="py-24 bg-gray-50">
        <div className="max-w-6xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-900 mb-4">
              Admission Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Simple steps to join our community of learners
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Application", description: "Submit your application form online" },
              { step: "02", title: "Assessment", description: "Participate in our assessment process" },
              { step: "03", title: "Interview", description: "Meet with our admissions team" },
              { step: "04", title: "Enrollment", description: "Complete enrollment and begin your journey" }
            ].map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-[#07243C] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-medium">
                  {step.step}
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-center mt-12"
          >
            <Button size="lg" className="bg-[#07243C] text-white hover:bg-[#0a2d47]">
              Start Application
            </Button>
          </motion.div>
        </div>
      </section> */}

      <Footer />
    </div>
  );
}
